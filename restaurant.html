<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ReserVee - Restaurant Details</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="/src/assets/scss/main.scss" />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <a class="navbar-brand fw-bold" href="index.html">
          <h2 class="mb-0">ReserVee</h2>
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="index.html">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reservation.html">Reservations</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="admin.html">Admin</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="tools.html">Tools</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <main class="restaurant-page">
      <div class="container">
        <!-- Restaurant Header -->
        <section class="restaurant-header">
          <div class="restaurant-image">
            <img
              id="restaurantImage"
              src="https://images.pexels.com/photos/302899/pexels-photo-302899.jpeg"
              alt="Restaurant"
            />
          </div>
          <div class="restaurant-info">
            <h1 id="restaurantName">Restaurant Name</h1>
            <div class="restaurant-meta">
              <div class="rating">
                <span class="stars" id="restaurantRating">★★★★☆</span>
                <span class="rating-text" id="ratingText">(4.2/5)</span>
              </div>
              <div class="location" id="restaurantLocation">
                <span class="location-icon">📍</span>
                <span>Location</span>
              </div>
              <div class="cuisine" id="restaurantCuisine">
                <span class="cuisine-icon">🍽️</span>
                <span>Cuisine Type</span>
              </div>
            </div>
            <p id="restaurantDescription" class="restaurant-description">
              Restaurant description will be loaded here...
            </p>
            <div class="restaurant-actions">
              <button id="makeReservationBtn" class="cta-button">
                Make Reservation
              </button>
              <button id="viewMenuBtn" class="secondary-button">
                View Menu
              </button>
            </div>
          </div>
        </section>

        <!-- Restaurant Details -->
        <section class="restaurant-details">
          <div class="details-grid">
            <div class="detail-card">
              <h3>Opening Hours</h3>
              <div id="openingHours" class="opening-hours">
                <div class="hours-item">
                  <span>Monday - Friday:</span>
                  <span>8:00 AM - 10:00 PM</span>
                </div>
                <div class="hours-item">
                  <span>Saturday - Sunday:</span>
                  <span>9:00 AM - 11:00 PM</span>
                </div>
              </div>
            </div>

            <div class="detail-card">
              <h3>Contact Information</h3>
              <div id="contactInfo" class="contact-info">
                <div class="contact-item">
                  <span class="contact-icon">📞</span>
                  <span id="restaurantPhone">+90 ************</span>
                </div>
                <div class="contact-item">
                  <span class="contact-icon">✉️</span>
                  <span id="restaurantEmail"><EMAIL></span>
                </div>
                <div class="contact-item">
                  <span class="contact-icon">📍</span>
                  <span id="restaurantAddress"
                    >Full address will be shown here</span
                  >
                </div>
              </div>
            </div>

            <div class="detail-card">
              <h3>Features</h3>
              <div id="restaurantFeatures" class="features-list">
                <span class="feature-tag">WiFi</span>
                <span class="feature-tag">Outdoor Seating</span>
                <span class="feature-tag">Pet Friendly</span>
                <span class="feature-tag">Parking</span>
              </div>
            </div>

            <div class="detail-card">
              <h3>Price Range</h3>
              <div id="priceRange" class="price-range">
                <span class="price-symbol">💰💰💰</span>
                <span class="price-text">Moderate ($15-30 per person)</span>
              </div>
            </div>
          </div>
        </section>

        <!-- Menu Preview -->
        <section class="menu-preview">
          <h2>Popular Items</h2>
          <div id="menuItems" class="menu-grid">
            <!-- Menu items will be populated here -->
          </div>
        </section>

        <!-- Reviews Section -->
        <section class="reviews-section">
          <h2>Customer Reviews</h2>
          <div id="reviewsList" class="reviews-list">
            <!-- Reviews will be populated here -->
          </div>
        </section>
      </div>
    </main>

    <footer class="footer">
      <div class="container">
        <p>&copy; 2024 ReserVee Café. All rights reserved.</p>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <script type="module" src="/src/assets/js/restaurant.js"></script>
  </body>
</html>
