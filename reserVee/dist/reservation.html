<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ReserVee - Make a Reservation</title>
    <link rel="stylesheet" href="/reservee/assets/index-All4ik78.css" />
  </head>
  <body>
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <h2>ReserVee</h2>
        </div>
        <ul class="nav-menu">
          <li><a href="index.html" class="nav-link">Home</a></li>
          <li>
            <a href="reservation.html" class="nav-link active">Reservations</a>
          </li>
          <li><a href="admin.html" class="nav-link">Admin</a></li>
          <li><a href="tools.html" class="nav-link">Tools</a></li>
        </ul>
      </div>
    </nav>

    <main class="reservation-page">
      <div class="container">
        <section class="reservation-form-section">
          <h1>Make a Reservation</h1>
          <p class="form-description">
            Please fill out the form below to reserve your table.
          </p>

          <form id="reservationForm" class="reservation-form">
            <div class="form-group">
              <label for="fullName">Full Name *</label>
              <input type="text" id="fullName" name="fullName" required />
              <span class="error-message" id="fullNameError"></span>
            </div>

            <div class="form-group">
              <label for="phoneNumber">Phone Number *</label>
              <input type="tel" id="phoneNumber" name="phoneNumber" required />
              <span class="error-message" id="phoneNumberError"></span>
            </div>

            <div class="form-group">
              <label for="email">Email *</label>
              <input type="email" id="email" name="email" required />
              <span class="error-message" id="emailError"></span>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="date">Date *</label>
                <input type="date" id="date" name="date" required />
                <span class="error-message" id="dateError"></span>
              </div>

              <div class="form-group">
                <label for="time">Time *</label>
                <input type="time" id="time" name="time" required />
                <span class="error-message" id="timeError"></span>
              </div>
            </div>

            <div class="form-group">
              <label for="guests">Number of Guests *</label>
              <select id="guests" name="guests" required>
                <option value="">Select number of guests</option>
                <option value="1">1 Guest</option>
                <option value="2">2 Guests</option>
                <option value="3">3 Guests</option>
                <option value="4">4 Guests</option>
                <option value="5">5 Guests</option>
                <option value="6">6 Guests</option>
                <option value="7">7 Guests</option>
                <option value="8">8+ Guests</option>
              </select>
              <span class="error-message" id="guestsError"></span>
            </div>

            <div class="form-group">
              <label for="specialRequests">Special Requests</label>
              <textarea
                id="specialRequests"
                name="specialRequests"
                rows="4"
                placeholder="Any dietary restrictions, special occasions, or other requests..."
              ></textarea>
              <span class="error-message" id="specialRequestsError"></span>
            </div>

            <button type="submit" class="submit-button">Reserve Table</button>
          </form>

          <div id="successMessage" class="success-message hidden">
            <div class="success-content">
              <div class="success-icon">✓</div>
              <h3>Reservation Confirmed!</h3>
              <p>Thank you for your reservation. We'll see you soon!</p>
              <button id="makeAnother" class="secondary-button">
                Make Another Reservation
              </button>
            </div>
          </div>
        </section>
      </div>
    </main>

    <footer class="footer">
      <div class="container">
        <p>&copy; 2024 ReserVee Café. All rights reserved.</p>
      </div>
    </footer>

    <script type="module" src="/reservee/assets/index-oqU-VQ2N.js"></script>
  </body>
</html>
