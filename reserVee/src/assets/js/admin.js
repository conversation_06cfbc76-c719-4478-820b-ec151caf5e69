import { storage, formatDate, formatTime, debounce } from "./utils.js";

// Global variables for admin dashboard
let reservations = [];
let filteredReservations = [];
let currentSort = "date";

// DOM elements
let tableBody;
let totalCount;
let noReservationsDiv;
let tableContainer;
let dateFilter;
let sortSelect;
let clearAllButton;

// Initialize admin dashboard
function initAdminDashboard() {
  // Get DOM elements
  tableBody = document.getElementById("reservationsTableBody");
  totalCount = document.getElementById("totalReservations");
  noReservationsDiv = document.getElementById("noReservations");
  tableContainer = document.querySelector(".table-container");
  dateFilter = document.getElementById("dateFilter");
  sortSelect = document.getElementById("sortBy");
  clearAllButton = document.getElementById("clearAllData");

  // Check if required elements exist
  if (
    !tableBody ||
    !totalCount ||
    !noReservationsDiv ||
    !tableContainer ||
    !dateFilter ||
    !sortSelect ||
    !clearAllButton
  ) {
    console.error("Required admin dashboard elements not found");
    return;
  }

  loadReservations();
  renderReservations();
  attachEventListeners();
}

// Load reservations from storage
function loadReservations() {
  reservations = storage.get("reservations") || [];
  filteredReservations = [...reservations];
}

// Attach event listeners to dashboard elements
function attachEventListeners() {
  // Filter by date
  dateFilter.addEventListener("change", filterByDate);

  // Sort reservations
  sortSelect.addEventListener("change", (e) => {
    currentSort = e.target.value;
    sortReservations();
    renderReservations();
  });

  // Clear all data
  clearAllButton.addEventListener("click", clearAllData);

  // Auto-refresh every 30 seconds to catch new reservations
  setInterval(() => {
    loadReservations();
    applyFiltersAndSort();
    renderReservations();
  }, 30000);
}

// Filter reservations by selected date
function filterByDate() {
  const selectedDate = dateFilter.value;

  if (selectedDate) {
    filteredReservations = reservations.filter(
      (reservation) => reservation.date === selectedDate
    );
  } else {
    filteredReservations = [...reservations];
  }

  sortReservations();
  renderReservations();
}

// Sort reservations based on current sort criteria
function sortReservations() {
  filteredReservations.sort((a, b) => {
    switch (currentSort) {
      case "name":
        return a.fullName.localeCompare(b.fullName);
      case "guests":
        return parseInt(b.guests) - parseInt(a.guests);
      case "time":
        return a.time.localeCompare(b.time);
      case "date":
      default:
        const dateTimeA = new Date(`${a.date}T${a.time}`);
        const dateTimeB = new Date(`${b.date}T${b.time}`);
        return dateTimeA - dateTimeB;
    }
  });
}

// Apply current filters and sorting
function applyFiltersAndSort() {
  filterByDate();
}

// Render reservations table and update UI
function renderReservations() {
  updateStats();

  if (filteredReservations.length === 0) {
    showNoReservations();
    return;
  }

  hideNoReservations();
  renderTable();
}

// Update statistics display
function updateStats() {
  totalCount.textContent = `Total: ${filteredReservations.length}`;
}

// Show no reservations message
function showNoReservations() {
  tableContainer.style.display = "none";
  noReservationsDiv.style.display = "block";
}

// Hide no reservations message and show table
function hideNoReservations() {
  tableContainer.style.display = "block";
  noReservationsDiv.style.display = "none";
}

// Render the reservations table
function renderTable() {
  tableBody.innerHTML = "";

  filteredReservations.forEach((reservation) => {
    const row = createTableRow(reservation);
    tableBody.appendChild(row);
  });
}

// Create a table row for a reservation
function createTableRow(reservation) {
  const row = document.createElement("tr");

  row.innerHTML = `
    <td>${reservation.fullName}</td>
    <td>${reservation.phoneNumber}</td>
    <td>${reservation.email}</td>
    <td>${formatDate(reservation.date)}</td>
    <td>${formatTime(reservation.time)}</td>
    <td>${reservation.guests}</td>
    <td>${reservation.specialRequests || "-"}</td>
    <td>
      <button class="delete-btn" onclick="deleteReservation('${
        reservation.id
      }')"
              style="background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">
        Delete
      </button>
    </td>
  `;

  return row;
}

// Delete a specific reservation
function deleteReservation(id) {
  if (!confirm("Are you sure you want to delete this reservation?")) {
    return;
  }

  reservations = reservations.filter((reservation) => reservation.id !== id);
  storage.set("reservations", reservations);

  loadReservations();
  applyFiltersAndSort();
  renderReservations();
}

// Clear all reservation data
function clearAllData() {
  if (
    !confirm(
      "Are you sure you want to delete ALL reservations? This action cannot be undone."
    )
  ) {
    return;
  }

  storage.remove("reservations");
  loadReservations();
  applyFiltersAndSort();
  renderReservations();

  alert("All reservation data has been cleared.");
}

// Initialize admin dashboard when DOM is ready
document.addEventListener("DOMContentLoaded", initAdminDashboard);

// Export functions for potential external use and global access
export {
  initAdminDashboard,
  loadReservations,
  attachEventListeners,
  filterByDate,
  sortReservations,
  renderReservations,
  deleteReservation,
  clearAllData,
};

// Make deleteReservation globally accessible for onclick handlers
window.deleteReservation = deleteReservation;
