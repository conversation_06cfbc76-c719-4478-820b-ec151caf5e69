
// Navigation
.navbar {
  background-color: $white;
  box-shadow: $shadow-sm;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  @extend .container;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: $spacing-md;
  padding-bottom: $spacing-md;
}

.nav-logo {
  h2 {
    color: $primary-color;
    font-size: $font-size-2xl;
    font-weight: 700;
  }
}

.nav-menu {
  display: flex;
  gap: $spacing-lg;

  @media (max-width: $breakpoint-sm) {
    gap: $spacing-md;
  }
}

.nav-link {
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  transition: all $transition-fast;
  font-weight: 500;

  &:hover {
    background-color: $background-color;
    color: $primary-color;
  }

  &.active {
    background-color: $primary-color;
    color: $white;
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-md;
  font-weight: 500;
  text-decoration: none;
  transition: all $transition-fast;
  cursor: pointer;
  border: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.cta-button {
  @extend .btn;
  background-color: $primary-color;
  color: $white;
  font-size: $font-size-lg;
  padding: $spacing-md $spacing-xl;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;

  &:hover:not(:disabled) {
    background-color: color.adjust($primary-color, $lightness: -10%);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }

  &:disabled {
    background-color: color.adjust($primary-color, $lightness: 20%);
    transform: none;
    box-shadow: $shadow-sm;
  }
}

.submit-button {
  @extend .btn;
  background-color: $primary-color;
  color: $white;
  width: 100%;
  padding: $spacing-md;
  font-size: $font-size-lg;
  border-radius: $border-radius-md;

  &:hover {
    background-color: color.adjust($primary-color, $lightness: -10%);
  }
}

.secondary-button {
  @extend .btn;
  background-color: transparent;
  color: $primary-color;
  border: 2px solid $primary-color;

  &:hover {
    background-color: $primary-color;
    color: $white;
  }
}

.danger-button {
  @extend .btn;
  background-color: $error-color;
  color: $white;

  &:hover {
    background-color: color.adjust($error-color, $lightness: -10%);
  }
}

// Forms
.form-group {
  margin-bottom: $spacing-lg;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;

  @media (max-width: $breakpoint-sm) {
    grid-template-columns: 1fr;
  }
}

label {
  display: block;
  margin-bottom: $spacing-sm;
  font-weight: 500;
  color: $text-dark;
}

input,
select,
textarea {
  width: 100%;
  padding: $spacing-md;
  border: 2px solid color.adjust($text-light, $lightness: 40%);
  border-radius: $border-radius-md;
  font-size: $font-size-base;
  transition: border-color $transition-fast;

  &:focus {
    outline: none;
    border-color: $primary-color;
  }

  &.error {
    border-color: $error-color;
  }

  &:disabled {
    background-color: color.adjust($text-light, $lightness: 50%);
    cursor: not-allowed;
  }
}

textarea {
  resize: vertical;
  min-height: 100px;
}

.error-message {
  display: block;
  color: $error-color;
  font-size: $font-size-sm;
  margin-top: $spacing-xs;
}

// Cards
.card {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  overflow: hidden;
  transition: transform $transition-normal;

  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-lg;
  }
}

.feature-card {
  @extend .card;
  padding: $spacing-xl;
  text-align: center;

  .feature-icon {
    font-size: $font-size-4xl;
    margin-bottom: $spacing-md;
  }

  h3 {
    margin-bottom: $spacing-md;
    color: $primary-color;
  }

  p {
    color: $text-light;
    margin-bottom: 0;
  }
}

.tool-card {
  @extend .card;
  padding: $spacing-xl;

  h3 {
    margin-bottom: $spacing-lg;
    color: $primary-color;
    text-align: center;
  }
}

// Feature tags
.feature-tag {
  display: inline-block;
  background-color: $background-color;
  color: $primary-color;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  font-weight: 500;
  margin-right: $spacing-xs;
  margin-bottom: $spacing-xs;
}

// Tables
.table-container {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  overflow: hidden;
}

.reservations-table {
  width: 100%;

  th,
  td {
    padding: $spacing-md;
    text-align: left;
    border-bottom: 1px solid color.adjust($text-light, $lightness: 50%);
  }

  th {
    background-color: $background-color;
    font-weight: 600;
    color: $primary-color;
  }

  tbody tr:hover {
    background-color: $light-background;
  }
}
