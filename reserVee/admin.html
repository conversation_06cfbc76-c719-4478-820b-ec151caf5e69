<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ReserVee - Admin Dashboard</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="/src/assets/scss/main.scss" />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <a class="navbar-brand fw-bold" href="index.html">
          <h2 class="mb-0">ReserVee</h2>
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="index.html">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="reservation.html">Reservations</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="admin.html"
                >Admin</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="tools.html">Tools</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <main class="admin-page">
      <div class="container">
        <section class="admin-header">
          <h1>Admin Dashboard</h1>
          <div class="admin-controls">
            <div class="filter-group">
              <label for="dateFilter">Filter by Date:</label>
              <input type="date" id="dateFilter" />
            </div>
            <div class="sort-group">
              <label for="sortBy">Sort by:</label>
              <select id="sortBy">
                <option value="date">Date</option>
                <option value="name">Name</option>
                <option value="guests">Guests</option>
                <option value="time">Time</option>
              </select>
            </div>
            <button id="clearAllData" class="danger-button">
              Clear All Data
            </button>
          </div>
        </section>

        <section class="reservations-table-section">
          <div class="table-header">
            <h2>All Reservations</h2>
            <div class="table-stats">
              <span id="totalReservations">Total: 0</span>
            </div>
          </div>

          <div class="table-container">
            <table id="reservationsTable" class="reservations-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Phone</th>
                  <th>Email</th>
                  <th>Date</th>
                  <th>Time</th>
                  <th>Guests</th>
                  <th>Special Requests</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="reservationsTableBody">
                <!-- Reservations will be populated here -->
              </tbody>
            </table>

            <div id="noReservations" class="no-data">
              <div class="no-data-icon">📋</div>
              <h3>No Reservations Found</h3>
              <p>No reservations have been made yet.</p>
              <a href="reservation.html" class="cta-button"
                >Make First Reservation</a
              >
            </div>
          </div>
        </section>
      </div>
    </main>

    <footer class="footer">
      <div class="container">
        <p>&copy; 2024 ReserVee Café. All rights reserved.</p>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <script type="module" src="/src/assets/js/admin.js"></script>
  </body>
</html>
