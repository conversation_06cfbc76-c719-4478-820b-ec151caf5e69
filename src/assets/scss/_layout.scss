.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-md;

  @media (min-width: $breakpoint-md) {
    padding: 0 $spacing-xl;
  }
}

.grid {
  display: grid;
  gap: $spacing-md;

  &.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  &.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

.flex {
  display: flex;

  &.flex-col {
    flex-direction: column;
  }

  &.items-center {
    align-items: center;
  }

  &.justify-center {
    justify-content: center;
  }

  &.justify-between {
    justify-content: space-between;
  }

  &.gap-sm {
    gap: $spacing-sm;
  }

  &.gap-md {
    gap: $spacing-md;
  }

  &.gap-lg {
    gap: $spacing-lg;
  }
}

.mt-auto {
  margin-top: auto;
}

.mb-auto {
  margin-bottom: auto;
}
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@media (max-width: calc(#{$breakpoint-md} - 1px)) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: $breakpoint-md) {
  .hide-desktop {
    display: none !important;
  }
}
