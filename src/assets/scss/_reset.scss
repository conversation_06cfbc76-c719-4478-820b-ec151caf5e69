// CSS Reset
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  font-family: $font-family-primary;
  font-size: $font-size-base;
  line-height: 1.6;
  color: $text-dark;
  background-color: $light-background;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

main {
  display: block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin: 0 0 1rem;
}

a {
  color: $primary-color;
  text-decoration: none;
  transition: color $transition-fast;

  &:hover {
    color: color.adjust($primary-color, $lightness: -10%);
  }
}

img {
  max-width: 100%;
  height: auto;
}

button {
  border: none;
  background: none;
  font: inherit;
  cursor: pointer;
}

input,
button,
select,
textarea {
  font: inherit;
}

ul {
  list-style: none;
}

table {
  border-collapse: collapse;
  width: 100%;
}
