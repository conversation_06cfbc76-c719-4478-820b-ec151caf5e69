.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;

  .navbar-brand {
    text-decoration: none;

    h2 {
      color: white;
      font-weight: 700;
      font-size: 1.8rem;
      margin: 0;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;

    &:focus {
      box-shadow: none;
    }

    .navbar-toggler-icon {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }
  }

  .navbar-nav {
    .nav-item {
      margin: 0 0.25rem;

      .nav-link {
        color: rgba(255, 255, 255, 0.85) !important;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        transition: all 0.3s ease;

        &:hover {
          color: white !important;
          background-color: rgba(255, 255, 255, 0.1);
        }

        &.active {
          color: white !important;
          background-color: rgba(255, 255, 255, 0.2);
          font-weight: 600;
        }
      }
    }
  }
}

// Mobile responsive adjustments
@media (max-width: 991.98px) {
  .navbar {
    .navbar-collapse {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .navbar-nav {
      .nav-item {
        margin: 0.25rem 0;

        .nav-link {
          padding: 0.75rem 1rem;
          border-radius: 0.5rem;
        }
      }
    }
  }
}
.bg-primary {
  background-color: #40280b !important; // Dark blue-gray
}
