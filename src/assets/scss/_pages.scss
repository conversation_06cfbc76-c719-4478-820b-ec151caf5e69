// Page-specific styles

// Home page
.home-page {
  min-height: calc(100vh - 140px);
}

.hero {
  background: linear-gradient(
    135deg,
    $background-color 0%,
    $light-background 100%
  );
  padding: $spacing-3xl 0;
  text-align: center;

  h1 {
    font-size: $font-size-4xl;
    color: $primary-color;
    margin-bottom: $spacing-lg;
    font-weight: 700;

    @media (max-width: $breakpoint-sm) {
      font-size: $font-size-3xl;
    }
  }

  .hero-description {
    font-size: $font-size-lg;
    color: $text-light;
    max-width: 600px;
    margin: 0 auto $spacing-2xl;
    line-height: 1.6;
  }
}

// Location selector
.location-selector {
  max-width: 500px;
  margin: 0 auto $spacing-2xl;
}

.location-form {
  background-color: $white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;
  align-items: end;

  @media (max-width: $breakpoint-sm) {
    grid-template-columns: 1fr;
  }

  .form-group {
    margin-bottom: 0;

    &:last-child {
      grid-column: 1 / -1;
    }
  }

  label {
    font-size: $font-size-sm;
    margin-bottom: $spacing-xs;
  }

  select {
    padding: $spacing-sm $spacing-md;
  }
}

// Restaurant results
.restaurant-results {
  padding: $spacing-2xl 0;

  h2 {
    text-align: center;
    color: $primary-color;
    margin-bottom: $spacing-2xl;
    font-size: $font-size-3xl;
  }
}

.restaurant-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: $spacing-xl;

  @media (max-width: $breakpoint-sm) {
    grid-template-columns: 1fr;
  }
}

.restaurant-card {
  @extend .card;
  overflow: hidden;

  .restaurant-image {
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform $transition-normal;
    }
  }

  &:hover .restaurant-image img {
    transform: scale(1.05);
  }

  .restaurant-content {
    padding: $spacing-lg;
  }

  h3 {
    color: $primary-color;
    margin-bottom: $spacing-sm;
    font-size: $font-size-xl;
  }

  .restaurant-meta {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
    margin-bottom: $spacing-md;

    .rating {
      display: flex;
      align-items: center;
      gap: $spacing-xs;

      .stars {
        color: #ffd700;
      }

      .rating-text {
        font-size: $font-size-sm;
        color: $text-light;
      }
    }

    .cuisine,
    .price-range {
      font-size: $font-size-sm;
      color: $text-light;
    }
  }

  .restaurant-address {
    font-size: $font-size-sm;
    color: $text-light;
    margin-bottom: $spacing-md;
  }

  .restaurant-features {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-xs;
    margin-bottom: $spacing-lg;
  }

  .restaurant-actions {
    display: flex;
    gap: $spacing-sm;

    button {
      flex: 1;
      padding: $spacing-sm $spacing-md;
      border-radius: $border-radius-md;
      font-size: $font-size-sm;
      font-weight: 500;
      cursor: pointer;
      transition: all $transition-fast;
    }

    .view-details-btn {
      background-color: transparent;
      color: $primary-color;
      border: 2px solid $primary-color;

      &:hover {
        background-color: $primary-color;
        color: $white;
      }
    }

    .quick-reserve-btn {
      background-color: $primary-color;
      color: $white;
      border: 2px solid $primary-color;

      &:hover {
        background-color: color.adjust($primary-color, $lightness: -10%);
        border-color: color.adjust($primary-color, $lightness: -10%);
      }
    }
  }
}

.no-restaurants {
  grid-column: 1 / -1;
  text-align: center;
  padding: $spacing-3xl;

  .no-data-icon {
    font-size: $font-size-4xl;
    margin-bottom: $spacing-md;
    opacity: 0.5;
  }

  h3 {
    color: $text-light;
    margin-bottom: $spacing-md;
  }

  p {
    color: $text-light;
    margin-bottom: $spacing-lg;
  }
}

.features {
  padding: $spacing-3xl 0;

  h2 {
    text-align: center;
    color: $primary-color;
    margin-bottom: $spacing-2xl;
    font-size: $font-size-3xl;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-xl;
  }
}

// Restaurant detail page
.restaurant-page {
  min-height: calc(100vh - 140px);
  padding: $spacing-2xl 0;
}

.restaurant-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-2xl;
  margin-bottom: $spacing-3xl;

  @media (max-width: $breakpoint-md) {
    grid-template-columns: 1fr;
  }

  .restaurant-image {
    border-radius: $border-radius-lg;
    overflow: hidden;
    box-shadow: $shadow-md;

    img {
      width: 100%;
      height: 400px;
      object-fit: cover;

      @media (max-width: $breakpoint-md) {
        height: 300px;
      }
    }
  }

  .restaurant-info {
    display: flex;
    flex-direction: column;
    justify-content: center;

    h1 {
      color: $primary-color;
      font-size: $font-size-3xl;
      margin-bottom: $spacing-md;
    }

    .restaurant-meta {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;
      margin-bottom: $spacing-lg;

      .rating {
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        .stars {
          color: #ffd700;
          font-size: $font-size-lg;
        }
      }

      .location,
      .cuisine {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        color: $text-light;
      }
    }

    .restaurant-description {
      color: $text-light;
      line-height: 1.6;
      margin-bottom: $spacing-xl;
    }

    .restaurant-actions {
      display: flex;
      gap: $spacing-md;

      @media (max-width: $breakpoint-sm) {
        flex-direction: column;
      }
    }
  }
}

.restaurant-details {
  margin-bottom: $spacing-3xl;

  .details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: $spacing-xl;
  }

  .detail-card {
    @extend .card;
    padding: $spacing-xl;

    h3 {
      color: $primary-color;
      margin-bottom: $spacing-lg;
      text-align: center;
    }
  }

  .opening-hours,
  .contact-info {
    .hours-item,
    .contact-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-sm 0;
      border-bottom: 1px solid color.adjust($text-light, $lightness: 50%);

      &:last-child {
        border-bottom: none;
      }
    }

    .contact-item {
      justify-content: flex-start;
      gap: $spacing-md;
    }
  }

  .features-list {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
    justify-content: center;
  }

  .price-range {
    text-align: center;

    .price-symbol {
      font-size: $font-size-2xl;
      display: block;
      margin-bottom: $spacing-sm;
    }
  }
}

.menu-preview,
.reviews-section {
  margin-bottom: $spacing-3xl;

  h2 {
    text-align: center;
    color: $primary-color;
    margin-bottom: $spacing-2xl;
    font-size: $font-size-2xl;
  }
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: $spacing-lg;

  .menu-item {
    @extend .card;
    overflow: hidden;

    img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .menu-item-content {
      padding: $spacing-md;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h4 {
        color: $primary-color;
        margin: 0;
      }

      .price {
        font-weight: 600;
        color: $accent-color;
      }
    }
  }
}

.reviews-list {
  max-width: 800px;
  margin: 0 auto;

  .review-item {
    background-color: $white;
    padding: $spacing-lg;
    border-radius: $border-radius-md;
    box-shadow: $shadow-sm;
    margin-bottom: $spacing-md;

    &:last-child {
      margin-bottom: 0;
    }

    .review-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-sm;

      .reviewer-info {
        display: flex;
        align-items: center;
        gap: $spacing-md;

        .reviewer-name {
          font-weight: 600;
          color: $primary-color;
        }

        .review-rating {
          color: #ffd700;
        }
      }

      .review-date {
        font-size: $font-size-sm;
        color: $text-light;
      }
    }

    .review-comment {
      color: $text-light;
      line-height: 1.5;
      margin: 0;
    }
  }
}

// Reservation page
.reservation-page {
  min-height: calc(100vh - 140px);
  padding: $spacing-2xl 0;
}

.reservation-form-section {
  max-width: 600px;
  margin: 0 auto;

  h1 {
    text-align: center;
    color: $primary-color;
    margin-bottom: $spacing-md;
    font-size: $font-size-3xl;
  }

  .form-description {
    text-align: center;
    color: $text-light;
    margin-bottom: $spacing-2xl;
  }
}

.selected-restaurant-info {
  background-color: $white;
  padding: $spacing-lg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
  margin-bottom: $spacing-lg;

  .restaurant-selection {
    text-align: center;

    h3 {
      color: $primary-color;
      margin-bottom: $spacing-md;
    }

    .restaurant-details {
      display: flex;
      flex-direction: column;
      gap: $spacing-xs;
      margin-bottom: $spacing-md;

      strong {
        color: $primary-color;
        font-size: $font-size-lg;
      }

      span {
        color: $text-light;
        font-size: $font-size-sm;
      }
    }

    .change-restaurant-btn {
      background-color: transparent;
      color: $primary-color;
      border: 1px solid $primary-color;
      padding: $spacing-xs $spacing-md;
      border-radius: $border-radius-md;
      font-size: $font-size-sm;
      cursor: pointer;
      transition: all $transition-fast;

      &:hover {
        background-color: $primary-color;
        color: $white;
      }
    }
  }
}

.reservation-form {
  background-color: $white;
  padding: $spacing-2xl;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
}

// Admin page
.admin-page {
  min-height: calc(100vh - 140px);
  padding: $spacing-2xl 0;
}

.admin-header {
  margin-bottom: $spacing-2xl;

  h1 {
    color: $primary-color;
    margin-bottom: $spacing-lg;
    font-size: $font-size-3xl;
  }
}

.admin-controls {
  display: flex;
  gap: $spacing-lg;
  align-items: end;
  flex-wrap: wrap;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: stretch;
  }
}

.filter-group,
.sort-group {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  label {
    font-size: $font-size-sm;
    font-weight: 500;
  }

  input,
  select {
    width: auto;
    min-width: 150px;
  }
}

.reservations-table-section {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h2 {
      color: $primary-color;
      margin: 0;
    }

    .table-stats {
      color: $text-light;
      font-size: $font-size-sm;
    }
  }
}

@media (max-width: $breakpoint-lg) {
  .reservations-table {
    font-size: $font-size-sm;

    th,
    td {
      padding: $spacing-sm;
    }
  }
}

// Tools page
.tools-page {
  min-height: calc(100vh - 140px);
  padding: $spacing-2xl 0;
}

.tools-header {
  text-align: center;
  margin-bottom: $spacing-2xl;

  h1 {
    color: $primary-color;
    margin-bottom: $spacing-md;
    font-size: $font-size-3xl;
  }

  p {
    color: $text-light;
    font-size: $font-size-lg;
  }
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: $spacing-xl;

  @media (max-width: $breakpoint-sm) {
    grid-template-columns: 1fr;
  }
}

.tool-inputs {
  margin-bottom: $spacing-lg;
}

.input-group {
  margin-bottom: $spacing-md;

  &:last-child {
    margin-bottom: 0;
  }
}

.tool-result {
  border-top: 2px solid $background-color;
  padding-top: $spacing-md;

  .result-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacing-sm;

    &:last-child {
      margin-bottom: 0;
    }

    &.total {
      font-weight: 600;
      border-top: 1px solid $background-color;
      padding-top: $spacing-sm;
      color: $primary-color;
    }
  }
}

// Responsive adjustments
@media (max-width: $breakpoint-sm) {
  .hero {
    padding: $spacing-2xl 0;
  }

  .features {
    padding: $spacing-2xl 0;
  }

  .reservation-form {
    padding: $spacing-lg;
  }

  .tool-card {
    padding: $spacing-lg;
  }

  .restaurant-header {
    .restaurant-info h1 {
      font-size: $font-size-2xl;
    }
  }
}
