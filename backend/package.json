{"name": "reservee-backend", "version": "1.0.0", "description": "ReserVee Café Reservation System Backend API", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["reservations", "cafe", "restaurant", "api", "express", "nodejs"], "author": "ReserVee Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}