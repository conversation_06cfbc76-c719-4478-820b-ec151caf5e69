{"name": "reservee", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "backend:install": "cd backend && npm install", "backend:dev": "cd backend && npm run dev", "backend:start": "cd backend && npm start", "full:dev": "concurrently \"npm run backend:dev\" \"npm run dev\"", "full:install": "npm install && npm run backend:install"}, "devDependencies": {"vite": "^5.4.2", "sass": "^1.69.5", "concurrently": "^8.2.2"}}