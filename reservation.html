<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ReserVee - Make a Reservation</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="/src/assets/scss/main.scss" />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <a class="navbar-brand fw-bold" href="index.html">
          <h2 class="mb-0">ReserVee</h2>
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="index.html">Home</a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link active"
                aria-current="page"
                href="reservation.html"
                >Reservations</a
              >
            </li>
            <li class="nav-item">
              <a class="nav-link" href="admin.html">Admin</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="tools.html">Tools</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <main class="reservation-page">
      <div class="container">
        <section class="reservation-form-section">
          <h1>Make a Reservation</h1>
          <p class="form-description">
            Please fill out the form below to reserve your table.
          </p>

          <form id="reservationForm" class="reservation-form">
            <div class="form-group">
              <label for="fullName">Full Name *</label>
              <input type="text" id="fullName" name="fullName" required />
              <span class="error-message" id="fullNameError"></span>
            </div>

            <div class="form-group">
              <label for="phoneNumber">Phone Number *</label>
              <input type="tel" id="phoneNumber" name="phoneNumber" required />
              <span class="error-message" id="phoneNumberError"></span>
            </div>

            <div class="form-group">
              <label for="email">Email *</label>
              <input type="email" id="email" name="email" required />
              <span class="error-message" id="emailError"></span>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="date">Date *</label>
                <input type="date" id="date" name="date" required />
                <span class="error-message" id="dateError"></span>
              </div>

              <div class="form-group">
                <label for="time">Time *</label>
                <input type="time" id="time" name="time" required />
                <span class="error-message" id="timeError"></span>
              </div>
            </div>

            <div class="form-group">
              <label for="guests">Number of Guests *</label>
              <select id="guests" name="guests" required>
                <option value="">Select number of guests</option>
                <option value="1">1 Guest</option>
                <option value="2">2 Guests</option>
                <option value="3">3 Guests</option>
                <option value="4">4 Guests</option>
                <option value="5">5 Guests</option>
                <option value="6">6 Guests</option>
                <option value="7">7 Guests</option>
                <option value="8">8+ Guests</option>
              </select>
              <span class="error-message" id="guestsError"></span>
            </div>

            <div class="form-group">
              <label for="specialRequests">Special Requests</label>
              <textarea
                id="specialRequests"
                name="specialRequests"
                rows="4"
                placeholder="Any dietary restrictions, special occasions, or other requests..."
              ></textarea>
              <span class="error-message" id="specialRequestsError"></span>
            </div>

            <button type="submit" class="submit-button">Reserve Table</button>
          </form>

          <div id="successMessage" class="success-message hidden">
            <div class="success-content">
              <div class="success-icon">✓</div>
              <h3>Reservation Confirmed!</h3>
              <p>Thank you for your reservation. We'll see you soon!</p>
              <button id="makeAnother" class="secondary-button">
                Make Another Reservation
              </button>
            </div>
          </div>
        </section>
      </div>
    </main>

    <footer class="footer">
      <div class="container">
        <p>&copy; 2024 ReserVee Café. All rights reserved.</p>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <script type="module" src="/src/assets/js/reservation.js"></script>
  </body>
</html>
